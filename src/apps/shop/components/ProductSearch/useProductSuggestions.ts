import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys } from '@/libs/query/queryClient';
import { fetchApi } from '@/libs/utils/api';
import { StockStatusType } from '@/types';

type OrderedProduct = {
  productOfferId: string;
  productId: string;
  productName: string;
  vendorId: string;
  vendorName: string;
  lastOrderedAt: string;
  quantity: number;
  price: number;
  imageUrl: string;
  orderCount: number;
  stockStatus: StockStatusType;
};

export const useProductSuggestions = (
  query = '',
): {
  suggestions: string[];
  previouslyOrderedItems: OrderedProduct[];
  isSuggestionsLoading: boolean;
} => {
  const search = query.trim().toLowerCase();
  const [lastQueryWithNoResults, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(search, 300);

  const queryKey = queryKeys.products.suggestions(
    clinic?.id || '',
    debouncedQuery,
  );

  const shouldSkipQuery = () => {
    if (debouncedQuery?.length <= 2 || !clinic?.id) {
      return true;
    }

    const noResultsQuery =
      lastQueryWithNoResults &&
      debouncedQuery.startsWith(lastQueryWithNoResults);

    return noResultsQuery;
  };

  const {
    data = { suggestions: [], previouslyOrderedItems: [] },
    isLoading: isSuggestionsLoading,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await fetchApi<{
          data: string[];
          previouslyOrderedItems: OrderedProduct[];
        }>(
          `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(debouncedQuery)}&clinicId=${clinic?.id}`,
        );

        const suggestions = response?.data || [];

        if (suggestions.length === 0) {
          setLastEmptyQuery(debouncedQuery);
        } else {
          setLastEmptyQuery('');
        }

        return {
          suggestions,
          previouslyOrderedItems: [
            {
              productOfferId: '9d7581c7-e947-40f0-adb7-471859462816',
              productId: '01969b4c-645d-73bd-9e1d-2d31da2c7728',
              productName:
                'Simparica Chewables for Dogs 2.8 to 5.5 Pounds, Gold Label (3 Dose x 10)',
              vendorId: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
              vendorName: 'Zoetis',
              lastOrderedAt: '2025-08-18T17:07:29+00:00',
              quantity: 49,
              price: 4155,
              imageUrl:
                'https:\/\/ws.mwiah.com\/media\/image?id=9916dd3f-cc6c-4a81-836f-65bf913e7c69',
              orderCount: 15,
            },
            {
              productOfferId: '9d7581c7-ea9d-45a2-82c2-c44cf96f7978',
              productId: '9ed26eac-6dc5-418b-a451-f69a274881ce',
              productName: 'Simparica 6 x 20mg Light Brown 11.1 - 22lbs',
              vendorId: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
              vendorName: 'Zoetis',
              lastOrderedAt: '2025-08-14T12:51:03+00:00',
              quantity: 1,
              price: 7805,
              imageUrl:
                'https:\/\/shop.zoetis.com\/zb2b\/medias\/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjUyMzR8aW1hZ2UvanBlZ3xhRFUzTDJobFlTOHhNalUxTmpRMk1qQTVOalF4TkM4ek1EQlhlRE13TUVoZmJuVnNiQXw0NmI2NGFjMDBkMTI0MzEzZWM3YzhkN2NhMzE3ZWJkZDJiYTliZDkxNmRkYzBhZWM3MDJjMTkzZGZhY2Q3ZmFh',
              orderCount: 1,
            },
          ],
        };
      } catch (error) {
        setLastEmptyQuery(debouncedQuery);
        throw error;
      }
    },
    enabled: !shouldSkipQuery(),
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    const isBackSpacing =
      lastQueryWithNoResults &&
      debouncedQuery.length < lastQueryWithNoResults.length;

    if (isBackSpacing) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, lastQueryWithNoResults]);

  return {
    ...data,
    isSuggestionsLoading,
  };
};
