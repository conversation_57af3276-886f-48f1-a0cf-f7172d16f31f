import type { Meta, StoryObj } from '@storybook/react-vite';
import { Flex } from '@/libs/ui/Flex/Flex';
import { AddToCartButton } from './AddToCartButton';

const meta: Meta<typeof AddToCartButton> = {
  title: 'Product/AddToCartButton',
  component: AddToCartButton,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['icon', 'text'],
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    isDisabled: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Text: Story = {
  args: {
    variant: 'text',
    isLoading: false,
    isDisabled: false,
  },
};

export const Icon: Story = {
  args: {
    variant: 'icon',
    isLoading: false,
    isDisabled: false,
  },
};

export const Loading: Story = {
  args: {
    variant: 'text',
    isLoading: true,
    isDisabled: false,
  },
};

export const Disabled: Story = {
  args: {
    variant: 'text',
    isLoading: false,
    isDisabled: true,
  },
};

export const Comparison: Story = {
  render: () => (
    <Flex gap="md" align="center">
      <div style={{ width: '200px' }}>
        <AddToCartButton variant="text" />
      </div>
      <div style={{ width: '60px' }}>
        <AddToCartButton variant="icon" />
      </div>
    </Flex>
  ),
};
